<?php
namespace App\Support;

use Cache;
use App\Models\Task;
use Exception;

class CampaignHelpers
{
    /**
     * Fetch Cached settings from database
     *
     * @return string
     */
    public static function settings($key)
    {
        return Cache::get('social_keys')->where('key', $key)->first()->value;
    }
    
    /**
     * Get tasks based on campaign parameters
     * 
     * TODO it should be post_content_type (as the column name in influencer_request_details)
     *
     * @param string $media Social media platform (instagram, facebook, etc.)
     * @param string $type Campaign type (Boost me, Reaction video, Survey)
     * @param string $select_type Content type (Story, Story - Picture, etc.)
     * @param string $post_type_content Content category (content, photo, video)
     * @return \Illuminate\Database\Eloquent\Collection
     * @throws Exception
     */
    public static function getCampaignTasks($media, $type, $select_type, $post_type_content)
    {
        $tasks = Task::where(ucfirst('media'), $media);

        switch ($type) {
            case 'Boost me':
                $updated_select_type = $select_type;
                if ($post_type_content == 'content') {
                    $updated_select_type = 'Share Content - ' . $select_type;
                }

                if ($post_type_content == 'photo') {
                    $updated_select_type = 'Photo - ' . $select_type;
                }

                if ($post_type_content == 'video') {
                    $updated_select_type = 'Video - ' . $select_type;
                }
                $tasks
                    ->where(strtolower('type'), strtolower($type))
                    ->where(strtolower('select_type'), strtolower($updated_select_type));
                break;
            case 'Reaction video':
                $tasks
                    ->where(strtolower('type'), strtolower($type))
                    ->where(strtolower('select_type'), strtolower($select_type));
                break;
            case 'Survey':
                $tasks
                    ->where(strtolower('type'), strtolower($type))
                    ->where(strtolower('select_type'), strtolower($select_type));
                break;
            default:
                \Log::error('Invalid campaign type encountered', [
                    'media' => $media,
                    'type' => $type,
                    'select_type' => $select_type,
                    'post_content_type' => $post_type_content,
                    'class' => 'Helpers',
                    'method' => 'getCampaignTasks'
                ]);
                throw new Exception('Invalid campaign type.');
        }

        /**
         * TODO
         * Only for debugging purpose. Remove in the future
         */
        // Get the SQL query with bindings
        $query = $tasks->toSql();
        $bindings = $tasks->getBindings();

        // Replace ? with actual values for easier debugging
        $fullQuery = vsprintf(str_replace('?', "'%s'", $query), $bindings);

        // Log the full query and additional debugging details
        \Log::debug('Full Query in UserController::getInfluencerTasks()', [
            'query' => $fullQuery,
            'bindings' => $bindings,
            'request_data' => $request->all(),
            'ptc_count' => $ptc_count,
            'media' => $request->media,
            'type' => $request->type,
            'advertising' => $request->advertising,
            'post_content_type' => $request->post_content_type,
        ]);
        /** debugging ends */

        return $tasks->get();
    }
}
