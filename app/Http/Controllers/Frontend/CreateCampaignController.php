<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\NewRequestInfluencer;
use App\Models\AdminComission;
use App\Models\AdminGamification;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\Campaign;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\RequestTask;
use App\Models\SmCampaign;
use App\Models\Statistic;
use App\Models\Task;
use App\Models\User;
use App\Notifications\RequestInfluencer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Support\CampaignHelpers;

class CreateCampaignController extends Controller
{
    public function marketplaceNew()
    {

        if (Auth::user()->user_type == 'influencer') {
            return response()->view('errors.' . '404', [], 404);
        }
        $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns','campaigns.campaign_id','=','influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->where('campaigns.has_started', false)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc')->get();

        $open_campaigns = 0;
        if (isset($influencerData)) {
            foreach ($influencerData as $row) {
                if ($row->accept_request > 0 || $row->review != '2' && $row->finish != '0')
                    $open_campaigns++;
            }
        }


        $influencerDataActive =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc')->get();


        $active_campaigns = 0;

        foreach ($influencerDataActive as $row) {
            if ($row->invoice_id != NULL && $row->finish != '1')
                $active_campaigns++;
        }

        $total_campaigns  = $open_campaigns + $active_campaigns;
        if ($total_campaigns >= 15) {
            return redirect()->back()->with('info', 'Maximum number of campaign creation limit reached.');
        }
        $AdminComission = AdminComission::first();
        $sm_campaign_type = SmCampaign::groupBy('type')->get();
        $sm_campaign = SmCampaign::get();
        return view('front-user.pages.market-place-new', compact('AdminComission', 'sm_campaign_type', 'sm_campaign'));
    }

    public function marketplaceRequestSubmit(Request $request)
    {
        $formData = request()->except(['_token']);

        $lastCampaign = Campaign::orderBy('id', 'desc')->first();
        if ($lastCampaign) {
            $nextId = $lastCampaign->id + 1;
            $formData['compaign_id'] = 'C-' . str_pad($nextId, 8, '0', STR_PAD_LEFT);
        } else {
            throw new \Exception('Failed to create new campaign (id generation failed).');
        }

        $media = $request->mp_socialmedia;

        // possible values: Story, Story - Picture, Story - Video, Reel, Story - Picture, Story - Video
        $select_type = $request->mp_socialmedia_type2;
        // possible values: Boost me, Reaction video, Survey
        $type = $request->type_post_content;
        
        // possible values: content, photo, video
        $post_type_content = $ptc = $request->ptc;

        $tasks = CampaignHelpers::getCampaignTasks($media, $type, $select_type, $post_type_content);

        foreach ($request->influncer_selected_id as $row) {
            $InfluencerDetail = InfluencerDetail::where('id', $row)->first();

            $influencerUser = User::find($InfluencerDetail->user_id);
            if (empty($influencerUser)) {
                continue;
            }

            $AdvertisingMethodNewPrice = AdvertisingMethodNewPrice::where('user_id', $InfluencerDetail->user_id)
                ->where('type', $request->type_post_content)
                ->where('media', $request->mp_socialmedia)
                ->first();

            if ($influencerUser->is_small_business_owner) {
                $formData['current_price'] = $AdvertisingMethodNewPrice->type_price * 0.8;
            } else {
                $formData['current_price'] = ($AdvertisingMethodNewPrice->type_price + ($AdvertisingMethodNewPrice->type_price * 0.19)) * 0.8;
            }

            $detail = InfluencerRequestDetail::create([
                'user_id' => Auth::id(),
                'influencer_detail_id' => $InfluencerDetail->id,
                'advertising' => $request->mp_socialmedia_type2,
                'media' => $request->mp_socialmedia,
                'name' => $request->name,
                'social' => $request->mp_socialmedia,
                'time' => '10',
                'current_price' => $formData['current_price'],
                'discount_price' => $AdvertisingMethodNewPrice->type_price,
                'total_amount' => $request->total_amount,
                'compaign_id' => $formData['compaign_id'],
                'compaign_title' => $request->campaign_title,
                'influencer_price' => $request->price,
                'task' => isset($formData['task_additional']) ? implode(',', $formData['task_additional']) : '',
                'post_type' => $request->type_post_content,
                'post_content_type' => $request->ptc
            ]);

            $user = User::whereId(Auth::id())->first();

            $influencer = User::whereId($InfluencerDetail->user_id)->first();

            $results = AdminGamification::where('select_type', 'Point-Rules')->first();
            $influencer_request_details = InfluencerRequestDetail::where('influencer_detail_id', $InfluencerDetail->id)
                ->where('user_id', Auth::id())
                ->where('social_post_id', '!=', null)
                ->count();

            $influencer_request_details_row = InfluencerRequestDetail::where('influencer_detail_id', $InfluencerDetail->id)
                ->where('user_id', Auth::id())
                ->where('social_post_id', '!=', null)
                ->first();

            if (isset($influencer_request_details) && $influencer_request_details > 0) {
                Statistic::create([
                    'user_id' => $InfluencerDetail->user_id,
                    'points' => $results->repeat_bookings,
                    'type' => '1',
                    'title' =>  '[' . $formData['compaign_id'] . ']</br>' .
                        $results->repeat_bookings .
                        ' points gained for repeated booking from ' .
                        $influencer_request_details_row->user->first_name,
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }

            dispatch(new NewRequestInfluencer($influencer, $user, $detail));

            $influencer->notify(new RequestInfluencer($influencer, $user, $detail));
        }

        Campaign::create([
            'campaign_id' => $formData['compaign_id'],
            'campaign_title' => $request->campaign_title,
            'total_amount' => $request->total_amount
        ]);

        $details = InfluencerRequestDetail::where('compaign_id', $formData['compaign_id'])->first();
        if (isset($tasks)) {
            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'SocialMediaName') {
                    $mentions = 'mentions' . $task->id;
                    RequestTask::create([
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $details->compaign_id,
                        'task_id' => $task->id,
                        'value' => $request->$mentions,
                        'type' => $task->input_type
                    ]);
                }
            }

            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'Link') {
                    $site = 'site' . $task->id;
                    RequestTask::create([
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $details->compaign_id,
                        'task_id' => $task->id,
                        'value' => $request->$site,
                        'type' => $task->input_type
                    ]);
                }
            }

            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'UploadContent') {
                    $filepond = 'filepond' . $task->id;
                    if ($request->hasFile($filepond)) {
                        $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg', 'webp', 'heic'];
                        $filepond = $request->file($filepond);
                        $extension = $filepond->getClientOriginalExtension();
                        $filename = $filepond->store('files');
                    }
                    RequestTask::create([
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $details->compaign_id,
                        'task_id' => $task->id,
                        'value' => $filename,
                        'type' => $task->input_type
                    ]);
                }
            }

            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'Hashtag') {
                    $hashtag = 'hashtags' . $task->id;
                    $hashtags = [];
                    if (isset($request->$hashtag)) {
                        foreach ($request->$hashtag as $hash) {
                            if ($hash != '') {
                                $hasht = json_decode($hash);
                                foreach ($hasht as $tag) {
                                    array_push($hashtags, $tag->value);
                                }
                            }
                        }
                    }
                    RequestTask::create([
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $details->compaign_id,
                        'task_id' => $task->id,
                        'value' => implode(',', $hashtags),
                        'type' => 'Hashtag'
                    ]);
                }
            }

            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'Info') {
                    RequestTask::create([
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $details->compaign_id,
                        'task_id' => $task->id,
                        'type' => $task->input_type
                    ]);
                }
            }

            $task_additional = $request->task_additional;
            if (isset($task_additional)) {
                foreach ($task_additional as $additional) {
                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'SocialMediaName' && $task->id == $additional) {
                            $mentions = 'mentions' . $task->id;
                            RequestTask::create([
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $details->compaign_id,
                                'task_id' => $task->id,
                                'value' => $request->$mentions,
                                'type' => $task->input_type
                            ]);
                        }
                    }

                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'Link' && $task->id == $additional) {
                            $site = 'site' . $task->id;
                            RequestTask::create([
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $details->compaign_id,
                                'task_id' => $task->id,
                                'value' => $request->$site,
                                'type' => $task->input_type
                            ]);
                        }
                    }

                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'UploadContent' && $task->id == $additional) {
                            $filepond = 'filepond' . $task->id;
                            if ($request->hasFile($filepond)) {
                                $filepond = $request->file($filepond);
                                $extension = $filepond->getClientOriginalExtension();
                                $filename = $filepond->store('files');
                            }
                            RequestTask::create([
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $details->compaign_id,
                                'task_id' => $task->id,
                                'value' => $filename,
                                'type' => $task->input_type
                            ]);
                        }
                    }

                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'Hashtag' && $task->id == $additional) {
                            $hashtag = 'hashtags' . $task->id;
                            $hashtags = [];
                            if (isset($request->$hashtag)) {
                                foreach ($request->$hashtag as $hash) {
                                    if ($hash != '') {
                                        $hasht = json_decode($hash);
                                        foreach ($hasht as $tag) {
                                            array_push($hashtags, $tag->value);
                                        }
                                    }
                                }
                            }

                            RequestTask::create([
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $details->compaign_id,
                                'task_id' => $task->id,
                                'value' => implode(',', $hashtags),
                                'type' => 'Hashtag'
                            ]);
                        }
                    }

                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'Info' && $task->id == $additional) {
                            RequestTask::create([
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $details->compaign_id,
                                'task_id' => $task->id,
                                'type' => $task->input_type
                            ]);
                        }
                    }
                }
            }
        }

        return redirect('/open-campaigns')->with('success', 'Request sent successfully.');
    }
}
