@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Task
@endsection

@section('content')
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Edit Task</h1>
                </div>
            </div>
        </div>
    </section>
    
    <section class="content">
        <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
            @csrf
            <fieldset>
                <div class="am-selected-media">
                    <span id="error-select-media"></span>
                    <ul class="media-box">
                        <li class="">
                            <input type="radio" name="media" value="Instagram" required @if($mainTask->media == 'Instagram') checked @endif data-parsley-errors-container="#error-socmedia" >
                            <img src="{{ asset('assets/front-end/images/icons/new_social_media_instagram.png') }}" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Facebook" required @if($mainTask->media == 'Facebook') checked @endif >
                            <img src="{{ asset('assets/front-end/images/icons/new_social_media_facebook.png') }}" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Tiktok" required @if($mainTask->media == 'Tiktok') checked @endif >
                            <img src="{{ asset('assets/front-end/images/icons/new_social_media_tiktok.png') }}" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Youtube" required @if($mainTask->media == 'Youtube') checked @endif >
                            <img src="{{ asset('assets/front-end/images/icons/new_social_media_youtube.png') }}" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Twitter" required @if($mainTask->media == 'Twitter') checked @endif >
                            <img src="{{ asset('assets/front-end/images/icons/new_social_media_twitter.png') }}" class=" icon" alt="">
                        </li>
                    </ul>
                    <span id="error-socmedia" style="margin-top: -23px; display: inherit; margin-bottom: 45px;"></span>
                </div>
                <div class="form-group border-box d-flex">
                    <div class="one-line ">
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Boost me" id="boost-me" required @if($mainTask->type == 'Boost me') checked @endif data-parsley-errors-container="#error-posttype">
                            <label for="boost-me">Boost me</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Reaction Video" id="reaction-video" required @if($mainTask->type == 'Reaction Video') checked @endif >
                            <label for="reaction-video">Reaction-Video</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Survey" id="survey" required  @if($mainTask->type == 'Survey') checked @endif  >
                            <label for="survey">Survey</label>
                        </div>
                    </div>
                </div>
                <span id="error-posttype" style="margin-top: -23px; display: inherit; margin-bottom: 45px;"></span>
                <div class="form-group" id="boost_content"   @if($mainTask->type != 'Boost me') style="display:none;" @endif>
                    <div class="form-group border-box d-flex">
                        <div class="one-line ">
                            <div class="custom-checkbox"> 
                                <input type="radio" name="select_type" value="Share Content - Story" id="share-content-story" required  @if($mainTask->select_type == 'Share Content - Story') checked @endif data-parsley-errors-container="#error-boostoption">
                                <label for="share-content-story">Share Content - Story</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Photo - Post - picture" id="photo-post-picture" required   @if($mainTask->select_type == 'Photo - Post - picture') checked @endif >
                                <label for="photo-post-picture">Photo - Post picture</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Photo - Story - picture" id="photo-story-picture" required  @if($mainTask->select_type == 'Photo - Story - picture') checked @endif  >
                                <label for="photo-story-picture">Photo - Story picture</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Video - Post - video" id="video-post-video" required   @if($mainTask->select_type == 'Video - Post - video') checked @endif >
                                <label for="video-post-video">Video - Post video</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Video - Story - video" id="video-story-video" required  @if($mainTask->select_type == 'Video - Story - video') checked @endif  >
                                <label for="video-story-video">Video - Story video</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Video - Reel" id="video-reel" required   @if($mainTask->select_type == 'Video - Reel') checked @endif >
                                <label for="video-reel">Video - Reel</label>
                            </div>
                        </div>
                    </div>
                    <span id="error-boostoption" style="margin-top: -23px; display: inherit; margin-bottom: 45px;"></span>
                </div> 

                <div class="form-group" id="reaction_content" @if($mainTask->type != 'Reaction Video') style="display:none;" @endif>
                    <div class="form-group border-box d-flex">
                        <div class="one-line ">
                            <div class="custom-checkbox">  
                                <input type="radio" name="select_type" value="Reel" id="reaction-reel" required @if($mainTask->select_type == 'Reel') checked @endif data-parsley-errors-container="#error-reaction-option">
                                <label for="reaction-reel">Reel</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" value="Story - Video" id="reaction-story-video" required @if($mainTask->select_type == 'Story - Video') checked @endif>
                                <label for="reaction-story-video">Story - Video</label>
                            </div>
                        </div>
                    </div>
                    <span id="error-reaction-option" style="margin-top: -23px; display: inherit; margin-bottom: 45px;"></span>
                </div>

                <div class="form-group" id="survey_content"  @if($mainTask->type != 'Survey') style="display:none;" @endif>
                    <div class="form-group border-box d-flex">
                        <div class="one-line ">
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" id="survey-story-picture" value="Story - Picture" required   @if($mainTask->select_type == 'Story - Picture') checked @endif>
                                <label for="survey-story-picture">Story - Picture</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" id="survey-story-video" value="Story - Video" required   @if($mainTask->select_type == 'Story - Video') checked @endif>
                                <label for="survey-story-video">Story - Video</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="radio" name="select_type" id="survey-reel" value="Reel" data-parsley-errors-container="#error-survey-option" required   @if($mainTask->select_type == 'Reel') checked @endif>
                                <label for="survey-reel">Reel</label>
                            </div>
                        </div>
                    </div>
                    <span id="error-survey-option" style="margin-top: -23px; display: inherit; margin-bottom: 45px;"></span>
                </div> 

                <div class="form-group"> 
                    <label class="nrtask" for="required-tasks-label">Required Tasks </label> 
                </div> 
                <div class="task-list-admin">
                    @foreach($tasks as $task)
                        @if($task->task_type == 'required')
                        <div class="take-list d-flex block optionBox"> 
                            <input type="hidden" name="task_id[]"  value="{{$task->id}}">

                            <input type="hidden" name="task_type[]" value="required">
                            <div class="form-group">
                                <input type="text" name="task[]" class="form-control taketitle" required placeholder="Required Task" value="{{$task->task}}" >
                            </div>
                            <div class="form-group">
                                <select name="input_type[]" class="form-control input_type" required>
                                    <option value="" >Select</option>
                                    <option @if($task->input_type == 'Hashtag') Selected @endif>Hashtag</option>
                                    <option @if($task->input_type == 'Link') Selected @endif>Link</option>
                                    <option @if($task->input_type == 'UploadContent') Selected @endif>UploadContent</option>
                                    <option value="SocialMediaName"  @if($task->input_type == 'SocialMediaName') Selected @endif>Text</option>
                                    <option  @if($task->input_type == 'Info') Selected @endif>Info</option>
                                </select>
                            </div>
                            <div class="form-group no-info">
                                <input type="text" class="form-control input_title" name="input_title[]" placeholder="Enter Input Title"  @if($task->input_type != 'Info') required  @endif   value="{{$task->input_title}}"  @if($task->input_type == 'Info') style="display:none" @endif >
                            </div>
                            <div class="form-group no-info">
                                <input type="text" class="form-control input_placeholder" name="input_placeholder[]" placeholder="Enter Input Placeholder"  @if($task->input_type != 'Info') required  @endif  value="{{$task->input_placeholder}}" @if($task->input_type == 'Info') style="display:none" @endif  >   
                            </div> 
                            <div class="form-group no-info">
                                <input type="number" class="form-control input_count" name="input_count[]" placeholder="Enter Count"  value="{{$task->count}}"  min="0">  
                            </div>
                            <div class="form-group no-info">
                                <input type="number" class="form-control hashtag_count" name="hashtag_count[]" placeholder="Enter Tag Count" value="{{$task->tag_count}}"  min="0" @if($task->tag_count==null || $task->tag_count=="") style="display: none;" @endif>   
                            </div>  
                            <button data-task="{{ $task->id }}" type="button" class="remove working-icon"><img src="{{ asset('assets/front-end/images/icons/admin-remove-icon.png') }}" alt=""></button>
                        </div>
                        @endif
                    @endforeach 
                    <button type="button" class="add_task working-icon"><img src="{{ asset('assets/front-end/images/icons/admin-add-icon.png') }}" alt=""></button>
                </div>

                <div class="form-group"> 
                    <label class="nrtask" for="additional-tasks-label">Additional Tasks </label> 
                </div> 
                <div class="task-list-admin block1">
                    @foreach($tasks as $task)
                        @if($task->task_type == 'additional')
                        <div class="take-list d-flex  optionBox"> 
                            <input type="hidden" name="task_id[]"  value="{{$task->id}}" >
                            <input type="hidden" name="task_type[]" value="additional">
                            <div class="form-group">
                                <input type="text" name="task[]"  class="form-control taketitle" placeholder="Additional Task" value="{{$task->task}}" > 
                            </div>
                            <div class="form-group">
                                <select name="input_type[]" class="form-control input_type" required>
                                    <option value="" >Select</option>
                                    <option @if($task->input_type == 'Hashtag') Selected @endif >Hashtag</option>
                                    <option @if($task->input_type == 'Link') Selected @endif   >Link</option>
                                    <option @if($task->input_type == 'UploadContent') Selected @endif   >UploadContent</option>
                                    <option value="SocialMediaName"  @if($task->input_type == 'SocialMediaName') Selected @endif  >Text</option>
                                    <option  @if($task->input_type == 'Info') Selected @endif >Info</option>
                                </select>
                            </div>
                            <div class="form-group no-info">
                                <input type="text" class="form-control input_title" name="input_title[]" placeholder="Enter Input Title"  @if($task->input_type != 'Info') required  @endif   value="{{$task->input_title}}"  @if($task->input_type == 'Info') style="display:none" @endif >
                            </div>
                            <div class="form-group no-info">
                                <input type="text" class="form-control input_placeholder" name="input_placeholder[]" placeholder="Enter Input Placeholder" @if($task->input_type != 'Info') required  @endif  value="{{$task->input_placeholder}}" @if($task->input_type == 'Info') style="display:none" @endif  >     
                            </div>
                            <div class="form-group no-info">
                                <input type="number" class="form-control input_count" name="input_count[]" placeholder="Enter Count"  value="{{$task->count}}" min="0">   
                            </div> 
                            <div class="form-group no-info">
                                <input type="number" class="form-control hashtag_count" name="hashtag_count[]" placeholder="Enter Tag Count" value="{{$task->tag_count}}"  min="0" @if($task->tag_count==null || $task->tag_count=="") style="display: none;" @endif>   
                            </div>  
                            <button type="button" class="remove1 working-icon" data-task="{{ $task->id }}"><img src="{{ asset('assets/front-end/images/icons/admin-remove-icon.png') }}" alt=""></button>
                        </div> 
                        @endif
                    @endforeach 
                </div>
                <button type="button" class="add_task1 working-icon mb-5"><img src="{{ asset('assets/front-end/images/icons/admin-add-icon.png') }}" alt=""></button>
                <input type="submit" class="btn btn-danger" value="Update">
            </fieldset>
        </form>
    </section>
@endsection

@section('admin_script_codes')
 
<script type="text/javascript">

$('.add_task').click(function() {
    $('.block:last').after('<div class="take-list d-flex">\
                        <input type="hidden" name="task_id[]"  value="0" >\
                            <input type="hidden" name="task_type[]" value="required">\
                            <div class="form-group">\
                                <input type="text" name="task[]" class="form-control taketitle" required placeholder="Required Task">\
                            </div>\
                            <div class="form-group">\
                                <select name="input_type[]" class="form-control input_type" required>\
                                    <option value="" >Select</option>\
                                    <option  >Hashtag</option>\
                                    <option  >Link</option>\
                                    <option  >UploadContent</option>\
                                    <option  value="SocialMediaName">Text</option>\
                                    <option >Info</option>\
                                </select>\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="text" class="form-control input_title" name="input_title[]" placeholder="Enter Input Title" required >\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="text" class="form-control input_placeholder" name="input_placeholder[]" placeholder="Enter Input Placeholder" required >\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="number" class="form-control input_count" name="input_count[]" placeholder="Enter Count"   min="0">\
                            </div> \
                            <div class="form-group no-info">\
                                <input type="number" class="form-control hashtag_count" name="hashtag_count[]" placeholder="Enter Tag Count"  min="0" style="display: none;">\
                            </div> \
                            <button type="button" class="remove working-icon"><img src="{{ asset("assets/front-end/images/icons/admin-remove-icon.png") }}" alt=""></button></div>');
});
$(document).on('click', '.remove', function() {
    var taskId = $(this).attr('data-task');
    if (typeof taskId !== 'undefined' && taskId !== false) {
       $.ajax({
        url : "/admin/remove-task/"+taskId,
        type : "get",
        dataType : 'json',
        success : function(result)
        {
           if(result.status==200)
           toastr.success(result.message)
        
        },
        error : function(result)
        {
            toastr.error('something went wrong')
        }
       })
     }
     $(this).closest('.take-list').remove();
 });

$('.add_task1').click(function() {
    $('.block1:last').after('<div class="take-list d-flex">\
                            <input type="hidden" name="task_id[]"  value="0" >\
                            <input type="hidden" name="task_type[]" value="additional">\
                            <div class="form-group">\
                                <input type="text" name="task[]" class="form-control taketitle" placeholder="Additional Task">\
                            </div>\
                            <div class="form-group">\
                                <select name="input_type[]" class="form-control input_type" required>\
                                    <option value="" >Select</option>\
                                    <option  >Hashtag</option>\
                                    <option  >Link</option>\
                                    <option  >UploadContent</option>\
                                    <option  value="SocialMediaName">Text</option>\
                                    <option >Info</option>\
                                </select>\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="text" class="form-control input_title" name="input_title[]" placeholder="Enter Input Title" required >\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="text" class="form-control input_placeholder" name="input_placeholder[]" placeholder="Enter Input Placeholder" required >\
                            </div>\
                            <div class="form-group no-info">\
                                <input type="number" class="form-control input_count" name="input_count[]" placeholder="Enter Count"   min="0">\
                            </div> \
                            <div class="form-group no-info">\
                                <input type="number" class="form-control hashtag_count" name="hashtag_count[]" placeholder="Enter Tag Count"  min="0" style="display: none;">\
                            </div> \
                            <button type="button" class="remove1 working-icon"><img src="{{ asset("assets/front-end/images/icons/admin-remove-icon.png") }}" alt=""></button></div>');
});
$(document).on('click', '.remove1', function() {
    var taskId = $(this).attr('data-task');
    if (typeof taskId !== 'undefined' && taskId !== false) {
       $.ajax({
        url : "/admin/remove-task/"+taskId,
        type : "get",
        dataType : 'json',
        success : function(result)
        {
           if(result.status==200)
           toastr.success(result.message)
        
        },
        error : function(result)
        {
            toastr.error('something went wrong')
        }
       })
     }
    $(this).closest('.take-list').remove();
 });


$(document).on('change', '.input_type', function() {
    if($(this).val() == 'Info'){
        // $('.input_title').hide();
        // $('.input_placeholder').hide();
        // $(this).siblings('.input_title').hide();
        // $(this).siblings('.input_placeholder').hide();

        // $(this).parent().find('.input_title').hide();
        // $(this).parent().find('.input_placeholder').hide();
        console.log('info');
        $(this).closest(".take-list").find('.input_title').hide();
        $(this).closest(".take-list").find('.input_placeholder').hide();

        $(this).closest(".take-list").find('.input_title').removeAttr('required');
        $(this).closest(".take-list").find('.input_title').next(".parsley-errors-list").remove();
        $(this).closest(".take-list").find('.input_placeholder').removeAttr('required');
        $(this).closest(".take-list").find('.input_placeholder').next(".parsley-errors-list").remove();

        $(this).closest(".take-list").find('.hashtag_count').hide(); 
        $(this).closest(".take-list").find('.taketitle').removeAttr('style'); 
    }else{

         if($(this).val() == 'Hashtag'){
            $(this).closest(".take-list").find('.taketitle').attr('style','width:257px !important;'); 
            $(this).closest(".take-list").find('.hashtag_count').show(); 
        }else{
            $(this).closest(".take-list").find('.taketitle').removeAttr('style'); 
            $(this).closest(".take-list").find('.hashtag_count').hide();
        }
        $(this).closest(".take-list").find('.input_title').show();
        $(this).closest(".take-list").find('.input_placeholder').show();

        $(this).closest(".take-list").find('.input_title').attr('required', 'required');
        $(this).closest(".take-list").find('.input_placeholder').attr('required', 'required');
    }
}); 


 </script>
@endsection
    
