@extends('admin.layouts.master_admin')

@section('page_title')
{{config('app.name')}} | Manage Task
@endsection


@section('content')
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Manage Task</h1>
                </div>

                <div class="col-sm-6 text-right">
                    <a href="{{Request::root()}}/admin/add-task" class="btn btn-success btn-gold-styled pull-right w-auto d-inline-flex"><i class="fa fa-plus"></i> Add Task</a>
                </div> 
            </div>
        </div>
    </section>

    <section class="content">
        <div class="card">
            <div class="card-header">
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip" title="Collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip" title="Remove">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="taskDatatable">
                        <thead>
                        <tr>
                            <th class="column-title">Created At</th>
                            <th class="column-title">Media</th>
                            <th class="column-title">Type</th>  
                            <th class="column-title">Sub Type</th>  
                            <th class="column-title text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($taskGroups as $taskGroup)
                            <tr>
                                <td>{{ $taskGroup->created_at }}</td>
                                <td>{{ $taskGroup->media }}</td> 
                                <td>{{ $taskGroup->type }}</td> 
                                <td>{{ $taskGroup->select_type }}</td> 
                                <td class="text-center">
                                    <a href="{{ URL::to('/admin/edit-task', ['id' => $taskGroup->id]) }}" title="Edit"><i class="fa fa-edit fa-fw fa-lg"></i></a>
                                    <a href="javascript:void(0);" class="deleteBusinessService" id="{{ $taskGroup->id }}" title="Delete"><i class="fa fa-trash fa-fw fa-lg"></i></a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
@endsection


@section('admin_script_codes')
<script>
    $(document).ready(function() {
        $('#taskDatatable').DataTable({
            "order": [[ 0, "desc" ]],
            "pageLength": 25,
            "columnDefs": [
                {
                    "targets": [ 0 ],
                    "visible": false,
                    "searchable": false
                },
                {
                    "targets": [2],
                    "orderable": false
                }
            ]
        });
    } );
</script>
<script type="text/javascript">
    $(document).ready(function () {
        $("#taskDatatable").on("click", ".deleteBusinessService", function (e) {
            e.preventDefault();
            let id = $(this).attr('id');
            swal({
                title: "Are you sure?",
                text: "You will not be able to recover this Task!",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel please!",
                closeOnConfirm: false,
                closeOnCancel: false
            },
            function (isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        type: "post",
                        url: "{{ url('/admin/delete-task') }}",
                        data: {
                            "_token": "{{ csrf_token() }}",
                            "id": id
                        },
                        success: function (response) {
                            if (response.status == "success") {
                                toastr.success(response.msg);

                                setTimeout(function () {
                                    location.reload();
                                }, 5000)

                            }
                            if (response.status == "error") {
                                toastr.info(response.msg);
                                setTimeout(function () {
                                    location.reload();
                                }, 5000)
                            }
                        }
                    });
                    swal("Deleted!", "Task deleted successfully.", "success");
                } else {
                    swal("Cancelled", "Task is safe :)", "error");
                }
            });
        });
    });
</script>
@endsection