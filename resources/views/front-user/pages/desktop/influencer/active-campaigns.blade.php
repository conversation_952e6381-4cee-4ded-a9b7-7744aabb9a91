<div data-bs-toggle="modal" data-bs-target="#campaignPhasesModal" style="cursor: pointer;">
    @if(isset($row->social_post_id) && $row->social_post_id != '')
        <div class="row campaign-status-badge-container">
            <span class="btn-review-phase campaign-status-badge">Review Phase</span>
        </div>
    @else
    <div class="row campaign-status-badge-container">
        <span class="btn-submit-phase campaign-status-badge">Submit Phase</span>
    </div>
    @endif
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 50%">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $row->compaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $row->compaign_title }}</h4>
            <div>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/new/brand_camp.svg') }}" width="20" height="20"> {{ $row->user->first_name }}
                    {{ $row->user->last_name }}
                </span>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}">
                    {{ $row->post_type }}
                </span>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/icons/campaigns-' . $row->media. '.svg') }}" alt="" style="height: 20px;width:20px">
                    {{ $row->advertising }}
                </span>
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details"
            style="width: 25%; display:flex; flex-direction:column;">
            <span class="text-success"
                style="font-size: 1.5em;font-weight: 700; padding: 5px;">
                @php
                $newLivetreamPrice = 0;
                $fieldName = $row->advertising . '_price';
                $user = App\Models\User::where('id', Auth::id())->first();
                if ($user->advertisingMethodPrice != null) {
                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                }
                @endphp

                € {{ number_format($row->current_price, 2) }}
            </span>
            <span class="submit-text-color" style=" padding: 5px;">
                @php
                    $time =
                        isset($row->request_time_accept) &&
                        $row->request_time_accept == '1'
                            ? $row->request_time + $row->time
                            : $row->time;

                    $created_date = date(
                        'Y-m-d H:i:s',
                        strtotime($row->created_at),
                    );
                    $updated_date = date(
                        'Y-m-d H:i:s',
                        strtotime($row->updated_at),
                    );
                    $campaignDate = date(
                        'Y-m-d H:i:s',
                        strtotime($updated_date . ' + ' . $time . ' days'),
                    );
                    $date = date('Y-m-d H:i:s');
                    $seconds = strtotime($campaignDate) - strtotime($date);

                    $days = floor($seconds / 86400);
                    if ($days < $time && $days >= 0) {
                        $hours = floor(($seconds - $days * 86400) / 3600);

                        $minutes = floor(
                            ($seconds - $days * 86400 - $hours * 3600) / 60,
                        );

                        $seconds = floor(
                            $seconds -
                                $days * 86400 -
                                $hours * 3600 -
                                $minutes * 60,
                        );
                    } else {
                        'Time Passed';
                    }
                @endphp

                <span class="timing"
                    style="display:block; background-color:transparent; font-size:25px"
                    id="timer{{ $row->compaign_id }}">
                </span>
            </span>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%;  display:flex; flex-direction:column;">
            <button class="btn  btn-show-details" style="width: 100%;"
                target="popup" data-bs-toggle="modal"
                data-bs-target="#requestForm{{ $row->id }}">Show Details
            </button>
            @foreach ($lists as $list)
                {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                @if (empty($list->influencerdetails))
                    @php continue; @endphp
                @endif
                @if (empty($list->invoices->receipt))
                @php continue; @endphp
                @endif
                <a class="btn btn-show-my-invoice" href="{{ $list->invoices->receipt }}" target="_blank"
                    style="width: 100%; margin-top: 10px; border: solid 1px #AD80FF; color: white; background-color:#AD80FF; padding: 2px;">
                    <img name="pdf-icon-btn" src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" />
                    My Invoice
                </a>
                @php break; @endphp
            @endforeach
            @if ($row->social_posts == '')
                @if ($row->social_posts == '' && $row->invoice_id != '' && $row->request_time_accept != '1')
                    @php $text = ''; @endphp
                    @if (isset($row->requested_time->request_time))
                        @php $text = 'You have already requested {{ $row->requested_time->request_time }} days'; @endphp
                        @if ($row->requested_time->status == '1')
                            @php $text .= '-  Accepted' @endphp
                        @endif
                        @if ($row->requested_time->status == '0')
                            @php $text .= '-  Rejected' @endphp
                        @endif @php $text .= '-  Rejected' @endphp
                    @endif
                @endif
            @endif

            @if (isset($row->influencer_request_accepts->complaints))
                @if ($row->influencer_request_accepts->complaints->status == 'Cancelled')
                    <input type="button" class="blueBtn smallBtn greensmallbtn " value="Completed">
                @elseif($row->influencer_request_accepts->complaints->status == 'Confirmed')
                    <input type="button" class="blueBtn smallBtn redbigBtn" value="Cancelled">
                @else
                    <input type="button" class="complaint-btn w-100" value="Brand has submitted complaint">
                @endif
            @elseif($row->refund_reason != '')
                <div class="ms-auto">
                    <input type="button" class="blueBtn smallBtn redbigBtn" value="Request is Cancelled">
                </div>
            @else
                @if ($row->social_posts == '' && $row->invoice_id != '')
                    <button class="btn btn-cancel-new"
                        style="width: 100%; margin-top: 3%;" target="popup"
                        data-bs-toggle="modal"
                        data-bs-target="#cancelRequest{{ $row->id }}">Cancel Campaign
                    </button>
                    <button
                        class="btn btn-show-result1 startTImer{{ $row->compaign_id }}"
                        value="Submit"
                        onclick="influencerInitiateCampaignSubmissionSubmitButton('{{ $row->id }}')"
                        style="width: 100%;margin-top: 3%;">Submit
                    </button>
                @endif

                @if (isset($row->influencer_request_accepts->rating_reviews) && $row->review == 1)
                    <div class="ms-auto">
                        <input type="button"
                            class="blueBtn smallBtn submittedReview"
                            value="Brand has submitted review">
                    </div>
                @endif
            @endif
        </div>
    </div>
</div>
