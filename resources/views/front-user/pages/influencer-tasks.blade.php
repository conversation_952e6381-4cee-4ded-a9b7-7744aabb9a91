@if (isset($tasks))
    <div class="form-group">
        <div class="custom-task-list velidation-additional-tasks">
            @if (isset($tasks[0]))
                @foreach ($tasks as $task)
                    @if ($task->task_type == 'additional')
                        @php
                            $taskSave = explode(',', $influencer_request_details->task);
                        @endphp
                        @foreach ($taskSave as $taskS)
                            @if ($taskS == $task->id)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="task[]"
                                        value="{{ $task->id }}"
                                        id="task-id{{ $influencer_request_details->id }}-{{ $task->id }}"
                                        data-parsley-errors-container="#error-task"
                                        data-parsley-error-message="Please confirm that all tasks have been completed." data-parsley-mincheck=""
                                        required>
                                    <label class="form-check-label"
                                        for="task-id{{ $influencer_request_details->id }}-{{ $task->id }}">
                                        {{ $task->task }}
                                    </label>
                                </div>
                            @endif
                        @endforeach
                    @endif
                @endforeach


            @endif
            @foreach ($tasks as $task)
                @if ($task->task_type == 'required')
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="task[]" value="{{ $task->id }}"
                            id="task-id{{ $influencer_request_details->id }}-{{ $task->id }}"
                            data-parsley-errors-container="#error-task"
                            data-parsley-error-message="Please confirm that all tasks have been completed." data-parsley-checkmin="1" required
                            >
                        <label class="form-check-label"
                            for="task-id{{ $influencer_request_details->id }}-{{ $task->id }}">
                            {{ $task->task }}
                        </label>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
@endif
<span id="error-task"></span>
