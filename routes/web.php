<?php

use App\Http\Controllers\Auth\AdminLoginController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Backend\AdminController;
use App\Http\Controllers\Backend\ImpersonateController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Frontend\ActiveCampaignsController;
use App\Http\Controllers\Frontend\CreateCampaignController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\MessagesController;
use App\Http\Controllers\Frontend\PagesController;
use App\Http\Controllers\Frontend\PaymentController;
use App\Http\Controllers\Frontend\UserController;
use App\Http\Controllers\Frontend\InfluencerSubmissionController;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Frontend\OnBoardingController;
use App\Http\Controllers\Frontend\OpenCampaignsController;
use App\Http\Controllers\Frontend\PayoutController;
use App\Http\Controllers\Frontend\StripeMarketplaceController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Home routes
Route::get('date-test', [Controller::class, 'testDate']);
Route::get('fb-test', [Controller::class, 'getInfo']);
Route::get('lang/{lang}',                                         [HomeController::class, 'changeLanguage'])->name('changeLang');
Route::get('/get-cities',                                         [HomeController::class, 'getCities']);
Route::post('/home-comming-soon',                                 [HomeController::class, 'saveHomeCommingSoon']);
Route::post('/home-comming-soon-en',                              [HomeController::class, 'saveHomeCommingSoon']);
Route::post('/home-comming-soon-de',                              [HomeController::class, 'saveHomeCommingSoonDe']);
Route::get('/',                                                   [HomeController::class, 'index']);
Route::get('/en',                                                 [HomeController::class, 'indexEn']);
Route::get('/de',                                                 [HomeController::class, 'indexDe']);
Route::get('/home',                                               [HomeController::class, 'index']);
Route::get('/home-de',                                            [HomeController::class, 'indexDe']);
Route::get('/unsubscribe/{id?}',                                  [HomeController::class, 'unsubscribe']);
Route::get('/subscribe/{lang?}/{id?}', [HomeController::class, 'subscribe']);
Route::post('/contact',                                           [HomeController::class, 'contact']);
Route::get('/faq',                                                [HomeController::class, 'faqs']);
Route::get('set-session',                                         [HomeController::class, 'setSession']);
Route::get('timezone',                                            [HomeController::class, 'timezone']);
Route::get('twitch/webhook/callback',                             [HomeController::class, 'twitchWebhook']);
Route::get('verify-user/{token}',                                 [HomeController::class, 'verify_user']);
Route::get('send-verification-mail/{email}',                      [HomeController::class, 'sendVerificationMail']);
Route::get('/unsubscribe/{id?}',                                  [HomeController::class, 'unsubscribe']);
Route::get('/support',                                            [HomeController::class, 'help']);

//Login
Route::get('/login-en',                                           [LoginController::class, 'showLoginForm']);
Route::get('/login-de',                                           [LoginController::class, 'showLoginFormDe']);
Route::post('/login-en',                                          [LoginController::class, 'login']);
Route::post('/login-de',                                          [LoginController::class, 'login']);
Route::get('/login',                                              [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login',                                             [LoginController::class, 'login'])->name('login.submit');
Route::get('/logout',                                             [LoginController::class, 'logout'])->name('logout');

//forget password
Route::get('/forgot-password',                                    [LoginController::class, 'forgotPassword']);
Route::post('/forgot-password',                                   [LoginController::class, 'forgetPassword']);
Route::get('/password/reset/{token}', function ($token) {
    return view('auth.new-password')->with(['token' => $token]);
});

//Activate Password
Route::get('/password/activate/{token}',                          [LoginController::class, 'activatePassword']);
Route::post('/password/activate/{token}',                         [LoginController::class, 'updateForgotPassword']);
Route::post('/password/reset/{token}',                            [LoginController::class, 'updateForgotPassword']);

//social login
Route::get('login/{provider?}',                                   [RegisterController::class, 'redirectToProvider']);
Route::get('login/callback/{provider}',                           [RegisterController::class, 'handleProviderCallback']);

//Registration
Route::get('/signup',                                             [RegisterController::class, 'showSignupForm'])->name('signup');
Route::post('/signup',                                            [RegisterController::class, 'register'])->name('register.submit');
Route::get('session-user-type',                                   [RegisterController::class, 'sessionUsertype']);

// Dash pannel
//save seesio user type
Route::get('send-verification-mail-profile/{email}',             [UserController::class, 'sendVerificationMailProfile']);
Route::get('verify-user-profile/{token}',                        [UserController::class, 'verify_user_profile']);

Route::middleware('auth')->group(function () {
    // For the brands
    Route::get('/open-campaigns', [OpenCampaignsController::class, 'buildOpenCampaignsPage']);
    
    // For the influencers
    Route::get('/open-requests', [OpenCampaignsController::class, 'buildOpenRequestsPage']);

    Route::get('/home', [HomeController::class, 'index']);
    Route::get('/influencer-onboarding', [UserController::class, 'influencerWizard'])->name('influencer.myService');

    Route::post('/request-start-campaign', [OpenCampaignsController::class, 'requestStartCampaign']);
    // API endpoint to create a stripe payment intent programmatically, when brand initiates a payment 
    // during the start campaign process.
    // Route::post('/stripe/create-setup-intent', [StripeMarketplaceController::class, 'createSetupIntent']);
    Route::post('/stripe/retrieve-payment-intent', [StripeMarketplaceController::class, 'retrievePaymentIntent']);
    Route::post('/stripe/create-payment-intent', [StripeMarketplaceController::class, 'createPaymentIntent']);
    Route::post('/stripe/process-transfer-start-campaign', [StripeMarketplaceController::class, 'processTransferAndStartCampaign']);
    Route::post('/stripe/save-card', [StripeMarketplaceController::class, 'saveCard']);
    // TODO yet to activate
    // Route::post('/stripe/webhook', [StripeMarketplaceController::class, 'handleWebhook']);
    
    Route::post('/update-influencer',                                [OpenCampaignsController::class, 'updateInfluencer']);
    Route::post('/request-form',                                     [OpenCampaignsController::class, 'requestForm']);
    Route::get('campaign-cancel/{campaign_id?}',                     [OpenCampaignsController::class, 'campaignCancel']);

    Route::get('/active-campaigns/{review?}/{complaint?}', [ActiveCampaignsController::class, 'myActiveCampaings']);
    Route::get('/request-cancel/{id?}',                              [ActiveCampaignsController::class, 'requestCancel']);


    Route::get('/my-rank',                                           [UserController::class, 'statistics']);
    Route::get('/create-order',                                      [UserController::class, 'createOrder']);
    Route::get('/setup-payment',                                     [UserController::class, 'setupPaymentAuth']);
    Route::get('/cash-out', [UserController::class, 'cashoutInfluencer']);
    Route::post('/get-paid-payment',                                 [PayoutController::class, 'getPaidPayment']);
    Route::get('/stripe/go/to/dashboard', [OnBoardingController::class, 'goToStripeDashboard']);
    Route::get('/mollie-payment',                                    [UserController::class, 'preparePayment'])->name('mollie.payment');
    Route::get('/payment-success',                                   [UserController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/onboarding-account-link', [OnBoardingController::class, 'onboardingAccountLink'])->name('createOnboardingAccountLink');
    Route::get('connect-stripe-account', [OnBoardingController::class, 'connectStripeAccount']);
    Route::get('disconnect-stripe',                                  [UserController::class, 'disconnectStripeAccount']);
    Route::get('/change-status/{status?}',                           [UserController::class, 'changeStatus']);
    Route::get('/fetch-states',                                      [UserController::class, 'fetchStates']);
    Route::get('/fetch-cities',                                      [UserController::class, 'fetchCities']);
    Route::get('/settings',                                          [UserController::class, 'settings']);

    /*** Campaign related actions */
    Route::get('/get-influencer-tasks', [UserController::class, 'getInfluencerTasks']);

    /**
     * When the influencer is visiting their active campaigns at /active-campaigns page,
     * then if they click on the Submit button, an ajax request will be sent to this
     * url and after the response is received, the (#requestSubmit' + id) modal
     * (in resources/views/front-user/modals/influencer/active-campaigns.blade.php) will be shown.
     */
    Route::get('/initiate-influencer-campaign-submission/{id?}', [InfluencerSubmissionController::class, 'initiateInfluencerCampaignSubmission']);

    /**
     * This is where the influencer confirm and submit the campaign content.
     */
    Route::post('/confirm-influencer-campaign-submission', [InfluencerSubmissionController::class, 'confirmInfluencerCampaignSubmission']);

    Route::get('/accept-request/{id?}', [UserController::class, 'acceptRequest']);
    Route::post('/pause-campaign', [UserController::class, 'pauseCampaign']);
    Route::get('payment-detail/{campaign_id?}', [UserController::class, 'paymentDetail']);
    Route::get('/request-review/{id?}', [UserController::class, 'requestReview']);
    Route::post('/finish-campaign/{id?}', [UserController::class, 'finishCampaign']);
    Route::get('/read-campaign/{id?}', [UserController::class, 'readCampaign']);
    Route::post('/request-more-time', [UserController::class, 'requestMoreTime']);
    
    // social connect
    // The endpoints can be
    // connect/facebook, connect/instagram, connect/twitter, connect/youtube, connect/tiktok..
    Route::get('connect/{provider?}', [UserController::class, 'redirectToProvider']);
    
    // The endpoints can be
    // callback/facebook, callback/instagram, callback/twitter, callback/youtube, callback/tiktok..
    Route::get('callback/{provider}', [UserController::class, 'handleProviderCallback']);
    Route::get('auth', [UserController::class, 'handleProviderCallback']);
    Route::get('api/callback/login/tiktok', [UserController::class, 'handleProviderCallback']);
    Route::get('auth/tiktok/callback', [UserController::class, 'handleProviderCallback']);

    Route::post('/time-request-accept',                              [UserController::class, 'timeRequestAccept']);
    Route::get('disconnect/{provider?}',                             [UserController::class, 'disconnectProvider']);
    Route::get('/latest-social-connect',                             [UserController::class, 'latestSocialconnect']);
    Route::get('/latest-social-connect-data',                        [UserController::class, 'latestSocialconnectData']);
    Route::get('/get-social-connect',                                [UserController::class, 'getSocialconnect']);
    Route::get('/latest-advertising',                                [UserController::class, 'latestAdvertising']);
    Route::get('/get-influencer-data',                               [UserController::class, 'getInfluencerData']);
    Route::get('/get-admin-pricing', [UserController::class, 'getAdminPricing']);
    Route::get('hashtags',                                           [UserController::class, 'hashtags']);

    Route::post('/influencer',                                       [UserController::class, 'saveInfluencerWizard']);
    Route::post('/save-influencer-form',                             [UserController::class, 'saveInfluencerForm']);
    Route::post('/save-influencer-price',                            [UserController::class, 'saveInfluencerPrice']);
    Route::get('/campaign-history/{complaint?}', [UserController::class, 'orderHistory']);
    Route::get('/reviews-customer',                                  [UserController::class, 'reviewsCustomer']);
    Route::post('/update-review',                                    [UserController::class, 'reviewsCustomerUpdate']);
    Route::get('/request-history', [UserController::class, 'requestHistory']);
    Route::get('/marketplace-search',                                [UserController::class, 'marketplaceSearch']);
    Route::get('/marketplace-sidebar',                               [UserController::class, 'marketplaceSidebar']);
    Route::get('/select-request/{id?}',                              [UserController::class, 'selectRequest']);
    Route::get('/remove-request/{id?}',                              [UserController::class, 'removeRequest']);
    Route::get('/remove-request-all',                                [UserController::class, 'removeRequestAll']);
    Route::get('/remove-influencer-request/{i_id?}/{id?}',           [UserController::class, 'removeInfluencerRequest']);
    Route::get('/request-now',                                       [UserController::class, 'requestNow']);
    Route::post('/request-now',                                      [UserController::class, 'requestNowSave']);

    Route::get('/create-campaign', [CreateCampaignController::class, 'marketplaceNew']);
    Route::post('/create-campaign', [CreateCampaignController::class, 'marketplaceRequestSubmit']);

    Route::get('/get-tasks', [UserController::class, 'getTasks']);
    Route::get('/get-tasks-input',                                   [UserController::class, 'getTasksInput']);
    Route::get('/get-influencers',                                   [UserController::class, 'getInfluencers']);
    Route::get('/market-step-filter',                                [UserController::class, 'marketStepFilter']);
    Route::post('/request-now-update',                               [UserController::class, 'requestNowUpdate']);

    Route::post('/submit-review', [UserController::class, 'submitReview']);
    Route::post('/submit-complaint',                                 [UserController::class, 'submitComplaint']);
    Route::post('/review-dispute',                                   [UserController::class, 'reviewDispute']);
    Route::post('/review-phase-closed', [UserController::class, 'reviewClosed']);
    Route::post('/contact-support',                                  [UserController::class, 'contactSupport']);
    Route::get('/notifications',                                     [UserController::class, 'notifications']);
    Route::get('/change-password',                                   [UserController::class, 'getchangePassword']);
    Route::post('/change-password',                                  [UserController::class, 'changePassword']);
    Route::get('/verify-email',                                      [UserController::class, 'verifyEmail']);
    Route::get('/my-profile', [UserController::class, 'myProfile']);
    Route::post('/my-profile', [UserController::class, 'updateBrandProfile']);
    Route::post('/update-brand-profile-image', [UserController::class, 'updateBrandProfileImage']);
    Route::post('/activate-deactivate-user',                         [UserController::class, 'activateDeactivateUser']);

    Route::get('/users',                                            [MessagesController::class, 'users']);
    Route::get('/load-latest-messages',                             [MessagesController::class, 'getLoadLatestMessages']);
    Route::post('/send',                                            [MessagesController::class, 'postSendMessage']);
    Route::get('/fetch-old-messages',                               [MessagesController::class, 'getOldMessages']);

    Route::get('/test-stripe-payment/{campaignId?}', [App\Http\Controllers\TestingController::class, 'testStripePayment']);
});

//admin
Route::prefix('admin')->group(function () {
    Route::get('login',                                      [AdminLoginController::class, 'showLoginForm'])->name('admin.login');
    Route::post('login',                                     [AdminLoginController::class, 'login'])->name('admin.login.submit');
    Route::get('logout',                                     [AdminLoginController::class, 'logout'])->name('admin.logout');

    Route::get('/fetch-cities',                              [UserController::class, 'fetchCities']);
    //forget password routes
    Route::get('forget-password',                            [AdminLoginController::class, 'forgetPassword']);
    Route::post('send-forget-password-link',                 [AdminLoginController::class, 'sendForgetPasswordResetLink']);
    Route::get('password-reset/{token}', function ($token) {
        return view('admin.reset-password')->with(['token' => $token]);
    });
    Route::post('update-forget-password',                    [AdminLoginController::class, 'updateAdminForgotPassword']);


    Route::get('/',                                          [AdminController::class, 'index'])->name('admin.dashboard');
    ##Admin profile management Routes
    Route::get('/notifications',                             [AdminController::class, 'notifications']);
    Route::get('/profile',                                   [AdminController::class, 'profile'])->name('admin.profile');
    Route::post('/update-admin-profile',                     [AdminController::class, 'updateAdminProfile']);
    Route::get('/change-password',                           [AdminController::class, 'changePassword'])->name('admin.change-password');
    Route::post('/update-admin-password',                    [AdminController::class, 'updateAdminPassword']);


    Route::get('/set_password/{id?}/{type?}',               [AdminController::class, 'setPasswordSend']);
    Route::get('/deactivate-user/{id?}',                    [AdminController::class, 'deactivateUserSend']);
    Route::get('/activate-user/{id?}',                      [AdminController::class, 'activateUserSend']);


    Route::get('/get-campaign_list',                        [AdminController::class, 'getCampaignList']);


    Route::get('/fetch-states',                             [AdminController::class, 'fetchStates']);
    Route::get('/fetch-states-admin',                       [AdminController::class, 'fetchStates']);

    //Manage customers
    Route::get('/manage-customers',                         [AdminController::class, 'manageCustomers']);
    Route::get('/add-customer',                             [AdminController::class, 'addCustomer']);
    Route::post('/add-customer',                            [AdminController::class, 'saveCustomer']);
    Route::get('/edit-customer/{id?}',                      [AdminController::class, 'editCustomer']);
    Route::post('/edit-customer/{id?}',                     [AdminController::class, 'updateCustomer']);
    Route::post('/delete-customer',                         [AdminController::class, 'deleteCustomer']);

    //Manage rink operators
    Route::get('/manage-influencers',                       [AdminController::class, 'manageInfluencers']);
    Route::get('/add-influencer',                           [AdminController::class, 'addInfluencer']);
    Route::post('/add-influencer',                          [AdminController::class, 'saveInfluencer']);
    Route::get('/edit-influencer/{id?}',                    [AdminController::class, 'editInfluencer']);
    Route::post('/edit-influencer/{id?}',                   [AdminController::class, 'updateInfluencer']);
    Route::post('/delete-influencer',                       [AdminController::class, 'deleteInfluencer']);

    //Manage all users with impersonation
    Route::get('/manage-users', [AdminController::class, 'manageUsers']);

    //Manage blogs
    Route::get('/manage-blogs',                             [AdminController::class, 'manageBlogs']);
    Route::get('/add-blog',                                 [AdminController::class, 'addBlog']);
    Route::post('/add-blog',                                [AdminController::class, 'saveBlog']);
    Route::get('/edit-blog/{id?}',                          [AdminController::class, 'editBlog']);
    Route::post('/edit-blog/{id?}',                         [AdminController::class, 'updateBlog']);
    Route::post('/delete-blog',                             [AdminController::class, 'deleteBlog']);
    Route::post('/activate-deactivate-blog',                [AdminController::class, 'activateDeactivateBlog']);

    //Manage faqs
    Route::get('/manage-faqs',                              [AdminController::class, 'manageFaqs']);
    Route::get('/add-faq',                                  [AdminController::class, 'addFaq']);
    Route::post('/add-faq',                                 [AdminController::class, 'saveFaq']);
    Route::get('/edit-faq/{id?}',                           [AdminController::class, 'editFaq']);
    Route::post('/edit-faq/{id?}',                          [AdminController::class, 'updateFaq']);
    Route::post('/delete-faq',                              [AdminController::class, 'deleteFaq']);
    Route::post('/activate-deactivate-faq',                 [AdminController::class, 'activateDeactivateFaq']);

    //Manage faq-topics
    Route::get('/manage-faq-topics',                        [AdminController::class, 'manageFaqTopics']);
    Route::get('/add-faq-topic',                            [AdminController::class, 'addFaqTopic']);
    Route::post('/add-faq-topic',                           [AdminController::class, 'saveFaqTopic']);
    Route::get('/edit-faq-topic/{id?}',                     [AdminController::class, 'editFaqTopic']);
    Route::post('/edit-faq-topic/{id?}',                    [AdminController::class, 'updateFaqTopic']);
    Route::post('/delete-faq-topic',                        [AdminController::class, 'deleteFaqTopic']);
    Route::post('/activate-deactivate-faq-topic',           [AdminController::class, 'activateDeactivateFaqTopic']);

    //Manage pages
    Route::get('/manage-pages',                             [AdminController::class, 'managePages']);
    Route::get('/add-page',                                 [AdminController::class, 'addPage']);
    Route::post('/add-page',                                [AdminController::class, 'savePage']);
    Route::get('/edit-page/{id?}',                          [AdminController::class, 'editPage']);
    Route::post('/edit-page/{id?}',                         [AdminController::class, 'updatePage']);
    Route::post('/delete-page',                             [AdminController::class, 'deletePage']);
    Route::post('/activate-deactivate-page',                [AdminController::class, 'activateDeactivatePage']);



    //Manage category
    Route::get('/manage-category',                          [AdminController::class, 'manageCategory']);
    Route::get('/add-category',                             [AdminController::class, 'addCategory']);
    Route::post('/add-category',                            [AdminController::class, 'saveCategory']);
    Route::get('/edit-category/{id?}',                      [AdminController::class, 'editCategory']);
    Route::post('/edit-category/{id?}',                     [AdminController::class, 'updateCategory']);
    Route::post('/delete-category',                         [AdminController::class, 'deleteCategory']);

    //Manage social
    Route::get('/manage-social',                            [AdminController::class, 'manageSocial']);
    Route::post('/manage-social',                           [AdminController::class, 'saveSocial']);


    //Manage modes
    Route::get('/manage-modes',                            [AdminController::class, 'manageModes']);
    Route::post('/manage-modes',                           [AdminController::class, 'saveModes']);
    Route::post('/make-live',                              [AdminController::class, 'makeLive']);

    //Manage dialogue
    Route::get('/manage-dialogue',                         [AdminController::class, 'manageDialogue']);
    Route::get('/edit-dialogue/{id?}',                     [AdminController::class, 'editDialogue']);
    Route::post('/edit-dialogue/{id?}',                    [AdminController::class, 'updateDialogue']);
    Route::post('/activate-deactivate-dialogue',           [AdminController::class, 'activateDeactivateDialogue']);


    //Manage CampaignTime
    Route::get('/manage-campaign-time',                    [AdminController::class, 'manageCampaignTime']);
    Route::post('/manage-campaign-time',                   [AdminController::class, 'saveCampaignTime']);


    //Manage complaints
    Route::get('/manage-complaints',                       [AdminController::class, 'manageComplaint']);
    Route::get('/manage-paused-campaigns',                 [AdminController::class, 'managePausedCampaigns']);
    Route::get('/update-complaint-status/{orderId}/{status}', [AdminController::class, 'updateComplaintStatus']);
    Route::post('/manage-complaints',                      [AdminController::class, 'saveComplaint']);

    Route::get('/manage-disputes',                         [AdminController::class, 'manageDispute']);

    //Manage comission
    Route::get('/manage-comission',                        [AdminController::class, 'manageComission']);
    Route::post('/manage-comission',                       [AdminController::class, 'saveComission']);
    //Manage Hashtag
    Route::get('/manage-hashtag',                          [AdminController::class, 'manageHashtag']);
    Route::post('/manage-hashtag',                         [AdminController::class, 'saveHashtag']);

    //Manage pricing
    Route::get('/manage-pricing',                          [AdminController::class, 'managePricing']);
    Route::get('/add-pricing',                             [AdminController::class, 'addPricing']);
    Route::post('/add-pricing',                            [AdminController::class, 'savePricing']);
    Route::get('/edit-pricing/{id?}',                      [AdminController::class, 'editPricing']);
    Route::post('/edit-pricing/{id?}',                     [AdminController::class, 'updatePricing']);
    Route::post('/delete-pricing',                         [AdminController::class, 'deletePricing']);

    //Manage gamification
    Route::get('/manage-gamification',                     [AdminController::class, 'manageGamification']);
    Route::get('/add-gamification',                        [AdminController::class, 'addGamification']);
    Route::post('/add-gamification',                       [AdminController::class, 'saveGamification']);
    Route::get('/edit-gamification/{id?}',                 [AdminController::class, 'editGamification']);
    Route::post('/edit-gamification/{id?}',                [AdminController::class, 'updateGamification']);
    Route::post('/delete-gamification',                    [AdminController::class, 'deleteGamification']);

    //Manage campaigns
    Route::get('/manage-campaigns',                        [AdminController::class, 'manageCampaign']);
    Route::post('/manage-campaigns',                       [AdminController::class, 'saveCampaign']);



    //Manage sm-campaign
    Route::get('/manage-sm-campaign',                      [AdminController::class, 'manageSmCampaign']);
    Route::post('/manage-sm-campaign',                     [AdminController::class, 'saveSmCampaign']);


    //Manage tasks
    Route::get('/manage-tasks', [AdminController::class, 'manageTask']);
    Route::get('/add-task',                                 [AdminController::class, 'addTask']);
    Route::post('/add-task',                                [AdminController::class, 'saveTask']);
    Route::get('/edit-task/{id?}', [AdminController::class, 'editTask']);
    Route::post('/edit-task/{id?}', [AdminController::class, 'updateTask']);
    Route::post('/delete-task',                             [AdminController::class, 'deleteTask']);
    Route::get('/remove-task/{id?}',                        [AdminController::class, 'removeTask']);

    // Impersonate routes for admin users
    Route::get('/impersonate/take/{id}/{guardName?}', [\App\Http\Controllers\Backend\ImpersonateController::class, 'take'])->name('impersonate');
    Route::get('/impersonate/leave', [\App\Http\Controllers\Backend\ImpersonateController::class, 'leave'])->name('impersonate.leave');
});

Route::get('/{slug?}',                      [HomeController::class, 'cms']);
Route::get('/{en?}/{slug?}',                [HomeController::class, 'cmsLang']);
Route::get('/download_youtue_video/{vid?}', function ($vid) {
    $youtube = new \Pecee\Http\Service\YouTubeDownload($vid);
    return $results = $youtube->download();

    // $dl = new Download($url = "https://www.youtube.com/watch?v=ws_kjwbBdJs", $format = "mp4", $download_path = "music" );

    // //Saves the file to specified directory
    // $media_info = $dl->download();
    // $media_info = $media_info->first();

    // // Return as a download
    // return response()->download($media_info['file']->getPathname());

});
